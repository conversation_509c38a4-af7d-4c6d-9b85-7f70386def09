package com.airdoc.mpd.detection.hrv

import android.content.Context
import android.util.AttributeSet
import android.webkit.JavascriptInterface
import android.webkit.PermissionRequest
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebSettings
import android.webkit.WebView
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.ui.web.CustomWebView
import com.airdoc.mpd.common.language.LanguageManager
import com.airdoc.mpd.device.DeviceManager
import org.json.JSONObject
import java.io.File
import java.io.IOException
import java.net.MalformedURLException
import java.net.URL

/**
 * FileName: HrvWebView
 * Author by lilin,Date on 2025/4/9 16:29
 * PS: Not easy to write code, please indicate.
 */
class HrvWebView : CustomWebView {

    companion object{
        private val TAG = HrvWebView::class.java.simpleName
    }

    constructor(context: Context) : super(context, null)

    constructor(context: Context, attributeSet: AttributeSet?) : super(context, attributeSet)

    constructor(context: Context, attributeSet: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attributeSet,
        defStyleAttr
    )

    init {
        // 启用混合内容（HTTP和HTTPS混合）
        settings.mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
        // 设置缓存模式 - 启用缓存
        settings.cacheMode = WebSettings.LOAD_DEFAULT
//        settings.cacheMode = WebSettings.LOAD_NO_CACHE
        // 启用DOM存储
        settings.domStorageEnabled = true
        // 启用数据库存储
        settings.databaseEnabled = true
        // 启用JavaScript（确保JavaScript可以正常工作）
        settings.javaScriptEnabled = true
        // 设置用户代理字符串
        settings.userAgentString = settings.userAgentString + " HrvWebView/1.0"
        // 启用文件访问
        settings.allowFileAccess = true
        // 启用内容访问
        settings.allowContentAccess = true

        Logger.d(TAG, msg = "HrvWebView - 配置已启用")
        Logger.d(TAG, msg = "  缓存模式: LOAD_DEFAULT")
        Logger.d(TAG, msg = "  DOM存储: 已启用")
        Logger.d(TAG, msg = "  数据库存储: 已启用")
        Logger.d(TAG, msg = "  JavaScript: 已启用")
        Logger.d(TAG, msg = "  文件访问: 已启用")
        Logger.d(TAG, msg = "  内容访问: 已启用")
        Logger.d(TAG, msg = "  混合内容: 已允许")
        Logger.d(TAG, msg = "  缓存路径: ${context.cacheDir.absolutePath}")

        setWebViewClient(HrvWebViewClient())
        setWebChromeClient(HrvWebChromeClient())
    }

    private var hrvActionListener: HrvActionListener? = null

    fun setHrvActionListener(listener: HrvActionListener?){
        hrvActionListener = listener
    }

    /**
     * 清除WebView缓存
     */
    fun clearWebViewCache() {
        try {
            Logger.d(TAG, msg = "开始清除HrvWebView缓存...")

            // 清除缓存（包括磁盘缓存和内存缓存）
            clearCache(true)

            // 清除历史记录
            clearHistory()

            // 清除表单数据
            clearFormData()

            // 清除SSL偏好设置
            clearSslPreferences()

            // 清除WebView存储（localStorage, sessionStorage等）
            clearMatches()

            Logger.d(TAG, msg = "HrvWebView缓存清除完成")
        } catch (e: Exception) {
            Logger.e(TAG, msg = "清除HrvWebView缓存异常: ${e.message}")
        }
    }

    interface HrvActionListener{
        fun onPageFinished()
        fun onFinish()
        fun goHome()
        fun onPrintPage()
        fun onReady()
        fun onStartGazeTracking()
        fun onStopGazeTracking()
        fun onGazeTrackingStatus(isEnabled: Boolean)
    }

    inner class HrvAction{

        @JavascriptInterface
        fun onFinish() {
            try {
                Logger.d(TAG, msg = "onFinish")
                post {
                    hrvActionListener?.onFinish()
                }
            } catch (e: Exception) {
                Logger.e(TAG, msg = "onFinish error: ${e.message}")
            }
        }

        @JavascriptInterface
        fun goHome() {
            try {
                Logger.d(TAG, msg = "goHome")
                post {
                    hrvActionListener?.goHome()
                }
            } catch (e: Exception) {
                Logger.e(TAG, msg = "goHome error: ${e.message}")
            }
        }

        @JavascriptInterface
        fun printPage() {
            try {
                Logger.d(TAG, msg = "printPage")
                post {
                    hrvActionListener?.onPrintPage()
                }
            } catch (e: Exception) {
                Logger.e(TAG, msg = "printPage error: ${e.message}")
            }
        }

        @JavascriptInterface
        fun ready(){
            try {
                Logger.d(TAG, msg = "ready")
                post {
                    hrvActionListener?.onReady()
                }
            } catch (e: Exception) {
                Logger.e(TAG, msg = "ready error: ${e.message}")
            }
        }

        @JavascriptInterface
        fun startGazeTracking() {
            try {
                Logger.d(TAG, msg = "🚀 JavaScript调用: startGazeTracking - 启动眼动追踪")
                Logger.d(TAG, msg = "  调用时间: ${System.currentTimeMillis()}")
                Logger.d(TAG, msg = "  监听器状态: ${if (hrvActionListener != null) "已设置" else "未设置"}")
                post {
                    hrvActionListener?.onStartGazeTracking()
                }
            } catch (e: Exception) {
                Logger.e(TAG, msg = "startGazeTracking error: ${e.message}")
            }
        }

        @JavascriptInterface
        fun stopGazeTracking() {
            try {
                Logger.d(TAG, msg = "🛑 JavaScript调用: stopGazeTracking - 停止眼动追踪")
                Logger.d(TAG, msg = "  调用时间: ${System.currentTimeMillis()}")
                Logger.d(TAG, msg = "  监听器状态: ${if (hrvActionListener != null) "已设置" else "未设置"}")
                post {
                    hrvActionListener?.onStopGazeTracking()
                }
            } catch (e: Exception) {
                Logger.e(TAG, msg = "stopGazeTracking error: ${e.message}")
            }
        }

        @JavascriptInterface
        fun setGazeTrackingEnabled(enabled: Boolean) {
            try {
                Logger.d(TAG, msg = "⚙️ JavaScript调用: setGazeTrackingEnabled - 设置眼动追踪状态")
                Logger.d(TAG, msg = "  启用状态: $enabled")
                Logger.d(TAG, msg = "  调用时间: ${System.currentTimeMillis()}")
                Logger.d(TAG, msg = "  监听器状态: ${if (hrvActionListener != null) "已设置" else "未设置"}")
                post {
                    hrvActionListener?.onGazeTrackingStatus(enabled)
                }
            } catch (e: Exception) {
                Logger.e(TAG, msg = "setGazeTrackingEnabled error: ${e.message}")
            }
        }

        @JavascriptInterface
        fun getInitialParams(): String {
            try {
                Logger.d(TAG, msg = "📋 JavaScript调用: getInitialParams - 获取初始参数")

                // 获取当前语言
                val userLanguage = LanguageManager.getUserLanguage(context)
                val languageCode = "${userLanguage.language}-${userLanguage.country}"

                // 获取设备SN
                val deviceSn = DeviceManager.getDeviceSn()

                // 构建JSON对象
                val params = JSONObject().apply {
                    put("language", languageCode)
                    put("sn", deviceSn)
                    put("timestamp", System.currentTimeMillis())
                }

                val result = params.toString()
                Logger.d(TAG, msg = "  返回参数: $result")
                Logger.d(TAG, msg = "  语言: $languageCode")
                Logger.d(TAG, msg = "  设备SN: $deviceSn")

                return result
            } catch (e: Exception) {
                Logger.e(TAG, msg = "getInitialParams error: ${e.message}")
                // 返回默认参数
                val defaultParams = JSONObject().apply {
                    put("language", "zh-CN")
                    put("sn", "unknown")
                    put("timestamp", System.currentTimeMillis())
                    put("error", e.message)
                }
                return defaultParams.toString()
            }
        }

        @JavascriptInterface
        fun getLocalFilePath(fileName: String): String {
            return try {
                Logger.d(TAG, msg = "Android接口调用: getLocalFilePath - 文件名: $fileName")

                // 使用Content URI替代直接文件路径
                val contentUri = com.airdoc.mpd.provider.MpdFileProvider.getConfigFileUri(fileName)
                val contentPath = contentUri.toString()

                // 确保目录存在
                val configDir = File(context.filesDir, "app_configs")
                if (!configDir.exists()) {
                    configDir.mkdirs()
                    Logger.d(TAG, msg = "  创建配置目录: ${configDir.absolutePath}")
                }

                Logger.d(TAG, msg = "  返回Content URI: $contentPath")
                contentPath
            } catch (e: Exception) {
                Logger.e(TAG, msg = "Android接口 getLocalFilePath error: ${e.message}")
                ""
            }
        }

    }

    inner class HrvWebViewClient : CustomWebViewClient(){
        override fun onPageFinished(view: WebView?, url: String?) {
            super.onPageFinished(view, url)
            hrvActionListener?.onPageFinished()
        }

        override fun shouldInterceptRequest(
            view: WebView?,
            request: WebResourceRequest?
        ): WebResourceResponse? {
            val url = request?.url.toString()

            // 只拦截特定资源，其他资源走网络
            if (shouldInterceptLocalResource(url)) {
                try {
                    val fileName: String = getFileNameFromUrl(url)
                    val `is` = assets.open("web/$fileName")
                    val return_info = WebResourceResponse(getMimeType(fileName), "UTF-8", `is`)
                    return return_info
                } catch (e: IOException) {
                    e.printStackTrace()
                }
            }
            // 不需要拦截的资源返回null，让WebView自己处理
            return null
        }
    }

    private fun getMimeType(fileName: String): String {
        // 根据文件扩展名返回MIME类型
        if (fileName.endsWith(".js")) {
            return "application/javascript"
        } else if (fileName.endsWith(".css")) {
            return "text/css"
        } else if (fileName.endsWith(".png")) {
            return "image/png"
        } else if (fileName.endsWith(".jpg") || fileName.endsWith(".jpeg")) {
            return "image/jpeg"
        } else if (fileName.endsWith(".woff2")) {
            return "font/woff2"
        }
        return "text/plain"
    }

    private fun shouldInterceptLocalResource(url: String): Boolean {
        // 定义需要本地加载的资源类型和路径
        val localPaths: List<String> = mutableListOf(
            "/opencv",
            "/mediapipe"
        )

        for (path in localPaths) {
            if (url.contains(path)) {
                return true
            }
        }
        return false
    }

    private fun getFileNameFromUrl(url: String): String {
        try {
            // 解析URL
            val urlObj = URL(url)
            // 获取路径部分
            var path = urlObj.path

            // 移除开头的斜杠(如果存在)
            if (path.startsWith("/")) {
                path = path.substring(1)
            }

            return path
        } catch (e: MalformedURLException) {
            // URL解析失败的情况下，使用简单的字符串处理
            val parts = url.split("//".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
            if (parts.size > 1) {
                // 取host之后的部分
                val index = parts[1].indexOf("/")
                if (index != -1) {
                    return parts[1].substring(index + 1)
                }
            }
            return url
        }
    }

    inner class HrvWebChromeClient : CustomWebChromeClient(){

        override fun onPermissionRequest(request: PermissionRequest?) {
            Logger.d(TAG, msg = "onPermissionRequest")
            request?.grant(request.resources)
        }
    }

}