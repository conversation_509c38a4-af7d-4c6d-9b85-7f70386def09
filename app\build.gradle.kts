plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    id("kotlin-android")
    id("kotlin-parcelize")
    id("kotlin-kapt")
}

android {
    namespace = "com.airdoc.mpd"
    compileSdk = 34

    defaultConfig {
        applicationId = "com.airdoc.mpd"
        minSdk = 29
        targetSdk = 34
        versionCode = 105
        versionName = "1.0.6"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"

        externalNativeBuild {
            cmake {
                // cppFlags ""
                cppFlags("-std=c++14")
                cppFlags("-stdlib=libc++")
                arguments("-DANDROID_STL=c++_shared")
                arguments("-DANDROID_TOOLCHAIN=clang")
                arguments("-DANDROID_API=34")

                abiFilters("arm64-v8a")
            }
        }

        // 指定支持的ABI
        ndk {
            abiFilters.add("arm64-v8a")
//            abiFilters.add("armeabi-v7a")
        }

        // 设置 NDK 版本
        ndkVersion = "25.1.8937393"
    }

    buildTypes {
        release {
            //代码进行优化、混淆和缩减
            isMinifyEnabled = true
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
            //移除未使用的资源文件
            isShrinkResources = true
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = "1.8"
    }
    externalNativeBuild {
        cmake {
            path = file("src/main/cpp/CMakeLists.txt")
            version = "3.22.1"
        }
    }
    buildFeatures {
        viewBinding = true
        buildConfig = true
    }
}

dependencies {

    implementation(files("libs/lepu-blepro-1.0.8.aar"))

    implementation(libs.ble)
    implementation(libs.stream.log.android)
    implementation(libs.stream.log.android.file)

    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)
    implementation(libs.material)
    implementation(libs.androidx.constraintlayout)
    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)

    //KTX
    implementation(libs.lifecycle.viewmodel.ktx)
    implementation(libs.lifecycle.livedata.ktx)
    implementation(libs.lifecycle.runtime.ktx)
    implementation(libs.fragment.ktx)
    implementation(libs.activity.ktx)
    implementation(libs.kotlinx.coroutines.android)

    //AndroidX 注解
    implementation(libs.annotation)

    //LibCommon
    implementation(libs.common)
    //WebSocket
    implementation(libs.java.websocket)
    //屏幕适配
    implementation(libs.autosize)

    //用于支持Android应用程序在使用了较多的方法数时突破Dalvik Executable (DEX) 文件的方法数限制
    implementation(libs.multidex)

    implementation(libs.gson)

    implementation(libs.mmkv.static)

    implementation(libs.glide)
    implementation(libs.glide.okhttp3.integration)
    annotationProcessor(libs.glide.compiler)
    implementation(libs.glide.transformations)

    implementation(libs.okgo)
    implementation(libs.okhttputils)

    implementation(libs.lottie)

    implementation(libs.live.event.bus.x)
    implementation(libs.lebx.processor.gson)

    //网络
    implementation(libs.okhttp)
    implementation(libs.retrofit)
    implementation(libs.retrofit.converter.gson)
    implementation(libs.okhttp.logging.interceptor)

    //CameraX
    implementation(libs.camera.core)
    implementation(libs.camera.camera2)
    implementation(libs.camera.view)
    implementation(libs.camera.lifecycle)
    implementation(libs.guava)

    //WorkManager KTX
    implementation(libs.work.runtime.ktx)

    //MPAndroidChart 图标
    implementation(libs.mp.android.chart)

    implementation(libs.media3.exoplayer)
    implementation(libs.media3.ui)
    implementation(libs.media3.common)

    //MQTT
    implementation(libs.lp.iot.linkkit)

    //扫码识别
    implementation(libs.barcode.scanning)
    //人脸检测
    implementation(libs.face.detection)
}