//package com.airdoc.mpd.ppg
//
//import android.content.Context
//import android.os.Environment
//import com.airdoc.component.common.log.Logger
//import com.airdoc.mpd.common.format
//import com.airdoc.mpd.ppg.bean.AnalysisResult
//import com.airdoc.mpd.ppg.bean.BandPowers
//import com.airdoc.mpd.ppg.bean.BandStatistics
//import com.airdoc.mpd.ppg.bean.FrequencyBands
//import com.airdoc.mpd.ppg.bean.FrequencyDomainParameters
//import com.airdoc.mpd.ppg.bean.PPGDataPoint
//import com.airdoc.mpd.ppg.bean.TimeDomainParameters
//import org.json.JSONObject
//import java.io.BufferedWriter
//import java.io.File
//import java.io.FileWriter
//import java.io.IOException
//import kotlin.Int
//import kotlin.collections.mutableListOf
//import kotlin.math.PI
//import kotlin.math.abs
//import kotlin.math.cos
//import kotlin.math.pow
//import kotlin.math.sin
//import kotlin.math.sqrt
//
///**
// * FileName: PPGManager
// * Author by lilin,Date on 2025/6/20 16:26
// * PS: Not easy to write code, please indicate.
// */
//object PPGManager {
//
//    private val TAG = PPGManager::class.java.simpleName
//    // 采样频率常量
//    const val SAMPLING_FREQUENCY = 1000.0 // Hz
//    const val MIN_RR_INTERVAL = 500.0 // ms - 调整为更适合PPG信号的范围
//    const val MAX_RR_INTERVAL = 1500.0 // ms - 扩大上限以适应PPG信号
//    const val DEFAULT_RESAMPLE_LEN = 512 // 可选64,128,256,512 - 增加到512以提高频率分辨率
//    const val DEFAULT_RESAMPLE_FREQ = 4.0 // 可选 长时间记录2.0，标准分析4.0，高精度分析8.0，10.0
//
//    /**
//     * 从PPG数据点列表中提取PPG值列表（用于兼容现有分析函数）
//     */
//    fun extractPPGValues(dataPoints: List<PPGDataPoint>): List<Double> {
//        return dataPoints.map { it.ppgValue }
//    }
//
//    /**
//     * 从PPG数据点列表中提取时间戳列表
//     */
//    fun extractTimestamps(dataPoints: List<PPGDataPoint>): List<Long> {
//        return dataPoints.map { it.timestamp }
//    }
//
//    /**
//     * 计算采样频率（基于时间戳）
//     */
//    fun calculateSamplingFrequency(dataPoints: List<PPGDataPoint>): Double {
//        if (dataPoints.size < 2) return SAMPLING_FREQUENCY
//
//        val totalTimeNs = dataPoints.last().timestamp - dataPoints.first().timestamp
//        val totalTimeSeconds = totalTimeNs / 1_000_000_000.0 // 转换为秒
//        val samplingFreq = (dataPoints.size - 1) / totalTimeSeconds
//
//        Logger.d(TAG, msg = "计算得到的采样频率: ${samplingFreq.format(2)} Hz")
//        return samplingFreq
//    }
//
//    /**
//     * 检测R波峰值并计算RR间期（重写版本，基于滤波后数据实现更精确的RR间隙计算）
//     */
//    fun detectRPeaksAndCalculateRR(ecgData: List<Double>): List<Double> {
//        // 应用时间窗口滤波
//        val filteredECGData = applyTimeBasedFilterToECGData(ecgData)
//        Logger.d(TAG, msg = "对ECG数据应用时间窗口滤波，数据长度: ${filteredECGData.size}")
//
//        // 计算滤波后数据的统计信息
//        val maxVal = filteredECGData.maxOrNull() ?: 0.0
//        val minVal = filteredECGData.minOrNull() ?: 0.0
//        val meanVal = filteredECGData.average()
//        val stdVal = sqrt(filteredECGData.map { (it - meanVal).pow(2) }.average())
//
//        Logger.d(TAG, msg = "滤波后数据统计: 最大值=${maxVal}, 最小值=${minVal}, 均值=${meanVal}, 标准差=${stdVal}")
//
//        // 使用多种峰值检测策略
//        val allPeaks = detectPeaksWithMultipleStrategies(filteredECGData, meanVal, stdVal, maxVal)
//        Logger.d(TAG, msg = "多策略检测到的总峰值数量: ${allPeaks.size}")
//
//        // 峰值验证和筛选
//        val validatedPeaks = validateAndFilterPeaks(filteredECGData, allPeaks)
//        Logger.d(TAG, msg = "验证后的峰值数量: ${validatedPeaks.size}")
//
//        // 计算RR间期
//        val rrIntervals = calculateRRIntervals(validatedPeaks)
//        Logger.d(TAG, msg = "计算得到的RR间期数量: ${rrIntervals.size}")
//
//        // 输出RR间期统计信息
//        if (rrIntervals.isNotEmpty()) {
//            val avgRR = rrIntervals.average()
//            val minRR = rrIntervals.minOrNull() ?: 0.0
//            val maxRR = rrIntervals.maxOrNull() ?: 0.0
//            Logger.d(TAG, msg = "RR间期统计: 平均=${avgRR.format(1)}ms, 最小=${minRR.format(1)}ms, 最大=${maxRR.format(1)}ms")
//        }
//
//        return rrIntervals
//    }
//
//    /**
//     * 使用多种策略检测峰值
//     */
//    private fun detectPeaksWithMultipleStrategies(data: List<Double>, meanVal: Double, stdVal: Double, maxVal: Double): List<Int> {
//        val allPeaks = mutableSetOf<Int>()
//
//        // 策略1: 基于统计阈值的峰值检测
//        val threshold1 = meanVal + 1.2 * stdVal
//        val peaks1 = detectPeaksWithThreshold(data, threshold1, 30, "统计阈值策略")
//        allPeaks.addAll(peaks1)
//
//        // 策略2: 基于最大值的峰值检测
//        val threshold2 = maxVal * 0.7
//        val peaks2 = detectPeaksWithThreshold(data, threshold2, 25, "最大值策略")
//        allPeaks.addAll(peaks2)
//
//        // 策略3: 自适应阈值峰值检测
//        val peaks3 = detectPeaksWithAdaptiveThreshold(data, 100, "自适应策略")
//        allPeaks.addAll(peaks3)
//
//        // 策略4: 基于信号导数的峰值检测
//        val peaks4 = detectPeaksWithDerivative(data, "导数策略")
//        allPeaks.addAll(peaks4)
//
//        return allPeaks.sorted()
//    }
//
//    /**
//     * 基于导数的峰值检测
//     */
//    private fun detectPeaksWithDerivative(data: List<Double>, strategyName: String): List<Int> {
//        val peaks = mutableListOf<Int>()
//
//        // 计算信号的一阶导数
//        val derivatives = mutableListOf<Double>()
//        for (i in 1 until data.size) {
//            derivatives.add(data[i] - data[i-1])
//        }
//
//        // 检测导数的零点（峰值位置）
//        for (i in 1 until derivatives.size) {
//            if (derivatives[i-1] > 0 && derivatives[i] <= 0) {
//                // 找到峰值位置
//                val peakIndex = i
//                if (peakIndex < data.size - 1) {
//                    // 验证是否为局部最大值
//                    if (data[peakIndex] > data[peakIndex-1] && data[peakIndex] > data[peakIndex+1]) {
//                        peaks.add(peakIndex)
//                    }
//                }
//            }
//        }
//        Logger.d(TAG, msg = "$strategyName: 检测到 ${peaks.size} 个峰值")
//        return peaks
//    }
//
//    /**
//     * 验证和筛选峰值
//     */
//    private fun validateAndFilterPeaks(data: List<Double>, peaks: List<Int>): List<Int> {
//        if (peaks.size < 2) return peaks
//
//        val validatedPeaks = mutableListOf<Int>()
//        val minPeakDistance = 15 // 减小最小峰值间距（采样点）
//        val maxPeakDistance = 300 // 增大最大峰值间距（采样点）
//
//        for (i in peaks.indices) {
//            val currentPeak = peaks[i]
//            val currentValue = data[currentPeak]
//
//            // 检查峰值强度（放宽条件）
//            //val isStrongPeak = isStrongPeak(data, currentPeak, currentValue)
//            val isStrongPeak = true
//
//            // 检查峰值间距
//            val isValidDistance = if (validatedPeaks.isNotEmpty()) {
//                val lastPeak = validatedPeaks.last()
//                val distance = currentPeak - lastPeak
//                distance >= minPeakDistance && distance <= maxPeakDistance
//            } else {
//                true
//            }
//
//            if (isStrongPeak && isValidDistance) {
//                validatedPeaks.add(currentPeak)
//            }
//        }
//
//        // 如果验证后的峰值太少，放宽条件重新验证
//        if (validatedPeaks.size < 5 && peaks.size > 10) {
//            Logger.d(TAG, msg = "验证后的峰值太少，放宽条件重新验证...")
//            return validateAndFilterPeaksRelaxed(data, peaks)
//        }
//
//        return validatedPeaks
//    }
//
//    /**
//     * 放宽条件的峰值验证
//     */
//    private fun validateAndFilterPeaksRelaxed(data: List<Double>, peaks: List<Int>): List<Int> {
//        val validatedPeaks = mutableListOf<Int>()
//        val minPeakDistance = 10 // 进一步减小最小峰值间距
//        val maxPeakDistance = 500 // 进一步增大最大峰值间距
//
//        for (i in peaks.indices) {
//            val currentPeak = peaks[i]
//            val currentValue = data[currentPeak]
//
//            // 检查峰值间距（主要条件）
//            val isValidDistance = if (validatedPeaks.isNotEmpty()) {
//                val lastPeak = validatedPeaks.last()
//                val distance = currentPeak - lastPeak
//                distance >= minPeakDistance && distance <= maxPeakDistance
//            } else {
//                true
//            }
//
//            // 简单的峰值验证
//            val isBasicPeak = if (currentPeak > 0 && currentPeak < data.size - 1) {
//                data[currentPeak] > data[currentPeak-1] && data[currentPeak] > data[currentPeak+1]
//            } else {
//                false
//            }
//
//            if (isBasicPeak && isValidDistance) {
//                validatedPeaks.add(currentPeak)
//            }
//        }
//
//        return validatedPeaks
//    }
//
//    /**
//     * 判断是否为强峰值（放宽条件）
//     */
//    private fun isStrongPeak(data: List<Double>, peakIndex: Int, peakValue: Double): Boolean {
//        if (peakIndex < 1 || peakIndex >= data.size - 1) return false
//
//        // 检查峰值是否高于相邻点
//        val isHigherThanNeighbors = data[peakIndex] > data[peakIndex-1] && data[peakIndex] > data[peakIndex+1]
//
//        // 检查峰值是否明显高于周围点（放宽条件）
//        val surroundingAvg = if (peakIndex >= 2 && peakIndex < data.size - 2) {
//            (data[peakIndex-1] + data[peakIndex-2] + data[peakIndex+1] + data[peakIndex+2]) / 4.0
//        } else {
//            (data[peakIndex-1] + data[peakIndex+1]) / 2.0
//        }
//
//        // 峰值应该比周围平均值高出一定比例（降低要求）
//        val threshold = surroundingAvg * 1.05 // 从1.1降低到1.05
//        val isSignificantlyHigher = peakValue > threshold
//
//        return isHigherThanNeighbors && isSignificantlyHigher
//    }
//
//    /**
//     * 计算RR间期
//     */
//    private fun calculateRRIntervals(peaks: List<Int>): List<Double> {
//        val rrIntervals = mutableListOf<Double>()
//
//        Logger.d(TAG, msg = "开始计算RR间期，峰值数量: ${peaks.size}")
//        Logger.d(TAG, msg = "峰值索引列表: ${peaks.take(10)}${if (peaks.size > 10) "..." else ""}")
//
//        for (i in 0 until peaks.size - 1) {
//            val peak1 = peaks[i]
//            val peak2 = peaks[i + 1]
//            val rrInterval = (peak2 - peak1) * (1000.0 / SAMPLING_FREQUENCY) // 转换为毫秒
//
//            //println("RR间期[${i+1}]: 峰值${i+1}(${peak1}) -> 峰值${i+2}(${peak2}), 间距=${rrInterval.format(1)}ms")
//
//            // 使用更宽松的RR间期范围
//            if (rrInterval in MIN_RR_INTERVAL..MAX_RR_INTERVAL) { // 600ms到1000ms，对应100-60bpm
//                rrIntervals.add(rrInterval)
//                // println("  -> 有效RR间期，已添加到列表")
//            } else {
//                // println("  -> 无效RR间期，超出范围")
//            }
//        }
//
//        Logger.d(TAG, msg = "RR间期计算完成，有效RR间期数量: ${rrIntervals.size}")
//        if (rrIntervals.isNotEmpty()) {
//            Logger.d(TAG, msg = "有效RR间期列表: ${rrIntervals.map { it.format(1) }}")
//        }
//
//        return rrIntervals
//    }
//
//    /**
//     * 改进的阈值峰值检测
//     */
//    private fun detectPeaksWithThreshold(data: List<Double>, threshold: Double, minDistance: Int, strategyName: String): List<Int> {
//        val peaks = mutableListOf<Int>()
//        var lastPeak = -1
//
//        for (i in 2 until data.size - 2) {
//            // 峰值检测条件：当前点大于阈值且大于相邻点
//            if (data[i] > threshold &&
//                data[i] > data[i-1] && data[i] > data[i+1] &&
//                data[i] > data[i-2] && data[i] > data[i+2]) {
//
//                // 避免重复检测
//                if (lastPeak == -1 || i - lastPeak >= minDistance) {
//                    peaks.add(i)
//                    lastPeak = i
//                }
//            }
//        }
//        Logger.d(TAG, msg = "$strategyName: 检测到 ${peaks.size} 个峰值")
//        return peaks
//    }
//
//    /**
//     * 改进的自适应阈值峰值检测
//     */
//    private fun detectPeaksWithAdaptiveThreshold(data: List<Double>, windowSize: Int, strategyName: String): List<Int> {
//        val peaks = mutableListOf<Int>()
//        var lastPeak = -1
//
//        for (i in windowSize until data.size - windowSize) {
//            // 计算局部窗口的统计信息
//            val window = data.subList(i - windowSize, i + windowSize)
//            val localMean = window.average()
//            val localStd = sqrt(window.map { (it - localMean).pow(2) }.average())
//            val localThreshold = localMean + 1.5 * localStd
//
//            // 峰值检测
//            if (data[i] > localThreshold &&
//                data[i] > data[i-1] && data[i] > data[i+1] &&
//                data[i] > data[i-2] && data[i] > data[i+2]) {
//
//                // 避免重复检测
//                if (lastPeak == -1 || i - lastPeak >= 30) {
//                    peaks.add(i)
//                    lastPeak = i
//                }
//            }
//        }
//        Logger.d(TAG, msg = "$strategyName: 检测到 ${peaks.size} 个峰值")
//        return peaks
//    }
//
//    /**
//     * 对ECG数据应用基于时间间隔的滤波（与绘图函数相同的滤波方法）
//     */
//    private fun applyTimeBasedFilterToECGData(ecgData: List<Double>): List<Double> {
//        if (ecgData.size < 3) return ecgData
//
//        // 计算采样间隔（基于采样频率）
//        val samplingIntervalNs = (1_000_000_000.0 / SAMPLING_FREQUENCY).toLong()
//        val filterWindow = 2 * samplingIntervalNs
//
//        Logger.d(TAG, msg = "采样间隔: ${samplingIntervalNs}ns")
//        Logger.d(TAG, msg = "滤波窗口: ${filterWindow}ns (2倍采样间隔)")
//
//        // 应用移动平均滤波
//        val filteredData = mutableListOf<Double>()
//
//        for (i in ecgData.indices) {
//            // 计算当前点对应的时间戳
//            val currentTimestamp = i * samplingIntervalNs
//
//            // 找到滤波窗口内的所有数据点
//            val windowData = mutableListOf<Double>()
//            for (j in ecgData.indices) {
//                val pointTimestamp = j * samplingIntervalNs
//                if (abs(pointTimestamp - currentTimestamp) <= filterWindow) {
//                    windowData.add(ecgData[j])
//                }
//            }
//
//            // 计算窗口内数据的平均值
//            val avgValue = windowData.average()
//            filteredData.add(avgValue)
//        }
//
//        Logger.d(TAG, msg = "ECG数据滤波完成，数据点数: ${filteredData.size}")
//        return filteredData
//    }
//
//    /**
//     * 计算时域参数
//     */
//    fun calculateTimeDomainParameters(rrIntervals: List<Double>): TimeDomainParameters {
//        if (rrIntervals.isEmpty()) {
//            throw IllegalArgumentException("RR间期数据为空")
//        }
//
//        // MeanNN - 平均RR间期
//        val meanNN = rrIntervals.average()
//
//        // SDNN - RR间期标准差
//        val sdnn = sqrt(rrIntervals.map { (it - meanNN).pow(2) }.average())
//
//        // 计算相邻RR间期差值
//        val rrDifferences = mutableListOf<Double>()
//        for (i in 0 until rrIntervals.size - 1) {
//            rrDifferences.add(abs(rrIntervals[i+1] - rrIntervals[i]))
//        }
//
//        // RMSSD - 相邻RR间期差值的均方根
//        val rmssd = sqrt(rrDifferences.map { it.pow(2) }.average())
//
//        // SDSD - 相邻RR间期差值的标准差
//        val meanDiff = rrDifferences.average()
//        val sdsd = sqrt(rrDifferences.map { (it - meanDiff).pow(2) }.average())
//
//        // pNN50 - 相邻RR间期差值>50ms的比例
//        val pnn50 = (rrDifferences.count { it > 50.0 }.toDouble() / rrDifferences.size) * 100.0
//
//        return TimeDomainParameters(meanNN, sdnn, rmssd, sdsd, pnn50)
//    }
//
//    /**
//     * 计算频域参数
//     */
//    fun calculateFrequencyDomainParameters(
//        rrIntervals: List<Double>,
//        resampleFreq: Double = DEFAULT_RESAMPLE_FREQ, // 支持自定义重采样频率
//        resampleLen: Int = DEFAULT_RESAMPLE_LEN      // 支持自定义重采样长度
//    ): FrequencyDomainParameters {
//        // 输入验证
//        validateInput(rrIntervals, resampleLen)
//
//        // 打印分析参数
//        printAnalysisParameters(resampleFreq, resampleLen, resampleLen, rrIntervals.size)
//
//        // 数据预处理
//        val (powerSpectrum, freqAxis) = preprocessDataForFrequencyAnalysis(rrIntervals, resampleFreq, resampleLen)
//        Logger.d(TAG,msg = "数据预处理: powerSpectrum = $powerSpectrum")
//
//        // 频段定义
//        val frequencyBands = defineFrequencyBands()
//
//        // 计算频段统计信息
//        val bandStatistics = calculateBandStatistics(freqAxis, frequencyBands)
//        Logger.d(TAG,msg = "频段频率点数量: VLF=${bandStatistics.vlfPoints}, LF=${bandStatistics.lfPoints}, HF=${bandStatistics.hfPoints}")
//
//        // 计算各频段功率
//        // VLF异常值修正
//        val powerSpectrumFixed = correctVlfOutliers(powerSpectrum, freqAxis)
//        val bandPowers = calculateBandPowers(powerSpectrumFixed, freqAxis, frequencyBands)
//
//        // 打印详细分析结果
//        printDetailedAnalysisResults(powerSpectrumFixed, freqAxis, frequencyBands, bandStatistics, bandPowers, resampleFreq, resampleLen)
//
//        return FrequencyDomainParameters(
//            bandPowers.totalPower * 100.0,
//            bandPowers.vlfPower * 100.0,
//            bandPowers.lfPower * 100.0,
//            bandPowers.hfPower * 100.0,
//            bandPowers.lfHfRatio,
//            bandPowers.stepPower
//        )
//    }
//
//    /**
//     * 输入验证
//     */
//    private fun validateInput(rrIntervals: List<Double>, resampleLen: Int) {
//        if (rrIntervals.size < resampleLen) {
//            throw IllegalArgumentException("RR间期数据点不足，至少需要${resampleLen}个点进行频域分析")
//        }
//    }
//
//    /**
//     * 计算最优重采样长度
//     */
//    private fun calculateOptimalResampleLength(dataSize: Int): Int {
//        return when {
//            dataSize >= 512 -> 512
//            dataSize >= 256 -> 256
//            dataSize >= 128 -> 128
//            else -> 64
//        }
//    }
//
//    /**
//     * 打印分析参数
//     */
//    private fun printAnalysisParameters(resampleFreq: Double, resampleLen: Int, adjustedResampleLen: Int, dataSize: Int) {
//        Logger.d(TAG, msg = "开始时域分析...\n" +
//                "重采样频率: $resampleFreq Hz\n" +
//                "原始重采样长度: ${resampleLen}\n" +
//                "调整后重采样长度: ${adjustedResampleLen}\n" +
//                "RR间期数量: $dataSize")
//    }
//
//    /**
//     * 数据预处理
//     */
//    private fun preprocessDataForFrequencyAnalysis(
//        rrIntervals: List<Double>,
//        resampleFreq: Double,
//        adjustedResampleLen: Int
//    ): Pair<List<Double>, List<Double>> {
//        // 重采样到固定频率
//        val resampledData = resampleRRIntervals(rrIntervals, resampleFreq, adjustedResampleLen)
//        Logger.d(TAG, msg = "重采样后数据长度: ${resampledData.size}")
//
//        val mean = resampledData.average()
//        val std = sqrt(resampledData.map { (it - mean).pow(2) }.average())
//        Logger.d(TAG, msg = "重采样数据统计: 均值=${mean.format(1)}ms, 标准差=${std.format(1)}ms")
//
//        // 应用窗函数
//        val windowedData = applyHammingWindow(resampledData)
//
//        // 计算功率谱密度
//        val (powerSpectrum, freqAxis) = calculatePowerSpectrumHRV(windowedData, resampleFreq)
//        Logger.d(TAG, msg = "功率谱计算完成，频率点数: ${freqAxis.size}\n" +
//                "频率范围: ${freqAxis.first().format(4)} - ${freqAxis.last().format(4)} Hz" +
//                "频率分辨率: ${(resampleFreq / adjustedResampleLen).format(4)} Hz")
//
//        return Pair(powerSpectrum, freqAxis)
//    }
//
//    /**
//     * 定义频段
//     */
//    private fun defineFrequencyBands(): FrequencyBands {
//        return FrequencyBands(
//            vlfRange = 0.003..0.04,
//            lfRange = 0.04..0.15,
//            hfRange = 0.15..0.4,
//            tpRange = 0.0..0.4
//        )
//    }
//
//    /**
//     * 计算频段统计信息
//     */
//    private fun calculateBandStatistics(freqAxis: List<Double>, bands: FrequencyBands): BandStatistics {
//        return BandStatistics(
//            vlfPoints = freqAxis.count { it in bands.vlfRange },
//            lfPoints = freqAxis.count { it in bands.lfRange },
//            hfPoints = freqAxis.count { it in bands.hfRange }
//        )
//    }
//
//    /**
//     * 计算各频段功率
//     */
//    private fun calculateBandPowers(
//        powerSpectrum: List<Double>,
//        freqAxis: List<Double>,
//        bands: FrequencyBands
//    ): BandPowers {
//        // 计算各频段功率
//        val vlfPower = integrateBandPower(powerSpectrum, freqAxis, bands.vlfRange)
//        val lfPower = integrateBandPower(powerSpectrum, freqAxis, bands.lfRange)
//        val hfPower = integrateBandPower(powerSpectrum, freqAxis, bands.hfRange)
//        val totalPower = vlfPower + lfPower + hfPower
//        val lfHfRatio = if (hfPower > 0) lfPower / hfPower else 0.0
//
//        val tpRange = 0.0..0.4
//        // 计算分段功率
//        val step = 0.01
//        val stepPower = mutableListOf<Double>()
//        //分段
//        val stepList = with(tpRange) {
//            val numSteps = kotlin.math.ceil((endInclusive - start) / step).toInt().coerceAtLeast(1)
//            (0 until numSteps).map { i ->
//                val startPoint = start + i * step
//                val endPoint = if (i == numSteps - 1) endInclusive else startPoint + step
//                startPoint..endPoint
//            }
//        }
//        //计算分段功率
//        stepList.forEach {
//            stepPower.add(integrateBandPower(powerSpectrum, freqAxis, it))
//        }
//
//        return BandPowers(vlfPower, lfPower, hfPower, totalPower, lfHfRatio,stepPower)
//    }
//
//    /**
//     * 修正VLF异常值
//     */
//    private fun correctVlfOutliers(powerSpectrum: List<Double>, freqAxis:List<Double>): List<Double> {
//        //LF和HF段最大值
//        val maxPower = powerSpectrum.zip(freqAxis).filter { it.second in 0.04..0.4 }.maxOf { it.first }
//        return powerSpectrum.map {
//            if (it > maxPower + 50){
//                maxPower + 50
//            }else{
//                it
//            }
//        }
//    }
//
//    /**
//     * 提取VLF异常值修正阈值：LF和HF段最大值（根据freqAxis精确分段）
//     */
//    private fun correctVlfOutliersThreshold(powerSpectrum: List<Double>, freqAxis: List<Double>): Double {
//        // LF: 0.04~0.15Hz, HF: 0.15~0.4Hz
//        val lfList = powerSpectrum.zip(freqAxis).filter { it.second >= 0.04 && it.second < 0.15 }.map { it.first }
//        val hfList = powerSpectrum.zip(freqAxis).filter { it.second >= 0.15 && it.second <= 0.4 }.map { it.first }
//        val maxLF = lfList.maxOrNull() ?: 0.0
//        val maxHF = hfList.maxOrNull() ?: 0.0
//        return maxOf(maxLF, maxHF)
//    }
//
//    /**
//     * 打印详细分析结果
//     */
//    private fun printDetailedAnalysisResults(
//        powerSpectrum: List<Double>,
//        freqAxis: List<Double>,
//        bands: FrequencyBands,
//        statistics: BandStatistics,
//        powers: BandPowers,
//        resampleFreq: Double,
//        adjustedResampleLen: Int
//    ) {
//        Logger.d(TAG, msg = "=== 功率谱密度详细分析 ===\n" +
//                "频率分辨率: ${(resampleFreq / adjustedResampleLen).format(6)} Hz")
//
//        // 获取各频段索引
//        val vlfIndices = freqAxis.mapIndexed { index, freq -> if (freq in bands.vlfRange) index else -1 }.filter { it >= 0 }
//        val lfIndices = freqAxis.mapIndexed { index, freq -> if (freq in bands.lfRange) index else -1 }.filter { it >= 0 }
//        val hfIndices = freqAxis.mapIndexed { index, freq -> if (freq in bands.hfRange) index else -1 }.filter { it >= 0 }
//
//        // 对VLF频段功率谱密度进行异常值过滤和替换，用于显示
////        val vlfPowerSpectrumForDisplay = filterVlfOutliersForDisplay(powerSpectrum, freqAxis, vlfIndices)
//
//        // 打印各频段功率谱密度值（VLF使用过滤后的值）
//        printBandPowerSpectrum("VLF", vlfIndices, freqAxis, powerSpectrum)
//        printBandPowerSpectrum("LF", lfIndices, freqAxis, powerSpectrum)
//        printBandPowerSpectrum("HF", hfIndices, freqAxis, powerSpectrum)
//
//        // 打印积分结果
//        printIntegrationResults(powers)
//
//        // 打印最终结果
//        printFinalResults(powers)
//    }
//
//    /**
//     * 为显示目的过滤VLF异常值
//     */
//    private fun filterVlfOutliersForDisplay(powerSpectrum: List<Double>, freqAxis: List<Double>, vlfIndices: List<Int>): List<Double> {
//        val vlfPowerSpectrumForDisplay = powerSpectrum.toMutableList()
//        val vlfPowers = vlfIndices.map { powerSpectrum[it] }
//
//        if (vlfPowers.isNotEmpty()) {
//            val sorted = vlfPowers.sorted()
//            val median = if (sorted.size % 2 == 0) (sorted[sorted.size/2-1] + sorted[sorted.size/2])/2 else sorted[sorted.size/2]
//            val threshold = median * 3
//            Logger.d(TAG,msg = "VLF异常值过滤: 中位数=${median.format(6)}, 阈值=${threshold.format(6)}")
//
//            vlfIndices.forEachIndexed { idx, i ->
//                if (vlfPowerSpectrumForDisplay[i] > threshold) {
//                    val prevIdx = if (idx > 0) vlfIndices[idx-1] else i
//                    val nextIdx = if (idx < vlfIndices.size-1) vlfIndices[idx+1] else i
//                    val prevVal = vlfPowerSpectrumForDisplay[prevIdx]
//                    val nextVal = vlfPowerSpectrumForDisplay[nextIdx]
//                    val originalVal = vlfPowerSpectrumForDisplay[i]
//                    val correctedVal = minOf(prevVal, nextVal, threshold)
//                    vlfPowerSpectrumForDisplay[i] = correctedVal
//                    Logger.d(TAG,msg = "  过滤异常值: f=${freqAxis[i].format(4)}Hz, 原值=${originalVal.format(6)} -> 修正值=${correctedVal.format(6)}")
//                }
//            }
//        }
//
//        return vlfPowerSpectrumForDisplay
//    }
//
//    /**
//     * 打印频段功率谱密度值
//     */
//    private fun printBandPowerSpectrum(bandName: String, indices: List<Int>, freqAxis: List<Double>, powerSpectrum: List<Double>) {
//        val s = StringBuilder("${bandName}频段功率谱密度值:")
//        indices.forEach { i ->
//            s.append("\n  f=${freqAxis[i].format(4)}Hz, P=${powerSpectrum[i].format(6)}")
//        }
//        Logger.d(TAG, msg = s.toString())
//    }
//
//    /**
//     * 打印积分结果
//     */
//    private fun printIntegrationResults(powers: BandPowers) {
//        Logger.d(TAG, msg = "=== 频段功率积分结果 ===\n" +
//                "VLF功率积分: ${powers.vlfPower.format(2)} ms²\n" +
//                "LF功率积分: ${powers.lfPower.format(2)} ms²\n" +
//                "HF功率积分: ${powers.hfPower.format(2)} ms²\n" +
//                "总功率: ${powers.totalPower.format(2)} ms²")
//    }
//
//    /**
//     * 打印最终结果
//     */
//    private fun printFinalResults(powers: BandPowers) {
//        Logger.d(TAG, msg = "频域参数计算结果:\n" +
//                "  总功率: ${powers.totalPower.format(2)} ms²\n" +
//                "  VLF功率: ${powers.vlfPower.format(2)} ms² (${(powers.vlfPower/powers.totalPower*100).format(1)}%)\n" +
//                "  LF功率: ${powers.lfPower.format(2)} ms² (${(powers.lfPower/powers.totalPower*100).format(1)}%)\n" +
//                "  HF功率: ${powers.hfPower.format(2)} ms² (${(powers.hfPower/powers.totalPower*100).format(1)}%)\n" +
//                "  LF/HF比值: ${powers.lfHfRatio.format(2)}")
//    }
//
//    /**
//     * 重采样RR间期到固定频率
//     */
//    private fun resampleRRIntervals(rrIntervals: List<Double>, targetFrequency: Double = 4.0, targetLength: Int = DEFAULT_RESAMPLE_LEN): List<Double> {
//        val cumulativeTime = mutableListOf<Double>()
//        var currentTime = 0.0
//        for (rr in rrIntervals) {
//            currentTime += rr / 1000.0 // ms->s
//            cumulativeTime.add(currentTime)
//        }
//        val resampledData = mutableListOf<Double>()
//        val timeStep = 1.0 / targetFrequency
//        for (i in 0 until targetLength) {
//            val targetTime = i * timeStep
//            val interpolatedValue = interpolateValue(cumulativeTime, rrIntervals, targetTime)
//            resampledData.add(interpolatedValue)
//        }
//        return resampledData
//    }
//
//    /**
//     * 生成频率数组
//     */
//    private fun generateFrequencies(dataSize: Int, samplingFreq: Double): List<Double> {
//        return (0 until dataSize/2+1).map { it * samplingFreq / dataSize }
//    }
//
//    /**
//     * 计算功率谱密度（国际HRV标准，单位ms²）
//     */
//    private fun calculatePowerSpectrumHRV(data: List<Double>, fs: Double): Pair<List<Double>, List<Double>> {
//        val n = data.size
//        val fft = fftComplex(data)
//        val power = MutableList(n/2+1) { 0.0 }
//        val freq = MutableList(n/2+1) { 0.0 }
//        for (k in 0..n/2) {
//            freq[k] = k * fs / n
//            val re = fft.first[k]
//            val im = fft.second[k]
//            // 直流分量不乘2，其余分量乘2
//            val scale = if (k == 0 || (n%2==0 && k==n/2)) 1.0 else 2.0
//            power[k] = scale * (re*re + im*im) / (n*n)
//        }
//        return Pair(power, freq)
//    }
//
//    /**
//     * 复数FFT实现，返回Pair<实部数组, 虚部数组>
//     */
//    private fun fftComplex(x: List<Double>): Pair<DoubleArray, DoubleArray> {
//        val n = x.size
//        if (n == 1) return Pair(doubleArrayOf(x[0]), doubleArrayOf(0.0))
//        if (n % 2 != 0) throw IllegalArgumentException("FFT输入长度必须为2的幂")
//        val even = fftComplex(x.filterIndexed { i, _ -> i % 2 == 0 })
//        val odd = fftComplex(x.filterIndexed { i, _ -> i % 2 == 1 })
//        val re = DoubleArray(n)
//        val im = DoubleArray(n)
//        for (k in 0 until n/2) {
//            val angle = -2.0 * Math.PI * k / n
//            val cosA = cos(angle)
//            val sinA = sin(angle)
//            val ore = odd.first[k]
//            val oim = odd.second[k]
//            val tre = cosA * ore - sinA * oim
//            val tim = sinA * ore + cosA * oim
//            re[k] = even.first[k] + tre
//            im[k] = even.second[k] + tim
//            re[k + n/2] = even.first[k] - tre
//            im[k + n/2] = even.second[k] - tim
//        }
//        return Pair(re, im)
//    }
//
//    /**
//     * 频段功率积分（梯形法，边界处理更严谨）
//     */
//    private fun integrateBandPower(power: List<Double>, freq: List<Double>, range: ClosedRange<Double>): Double {
//        var sum = 0.0
//        var trapezoidCount = 0
//        Logger.d(TAG, msg = "开始积分计算: 频段范围 ${range.start} - ${range.endInclusive} Hz")
//
//        for (i in 1 until freq.size) {
//            val f1 = freq[i-1]
//            val f2 = freq[i]
//            if (f2 < range.start) continue
//            if (f1 > range.endInclusive) break
//            val p1 = power[i-1]
//            val p2 = power[i]
//            // 只积分落在区间内的部分
//            val segStart = maxOf(f1, range.start)
//            val segEnd = minOf(f2, range.endInclusive)
//            Logger.d(TAG, msg = "积分落在区间: p1=${p1}, p2=${p2}, segStart=${segStart}, segEnd=${segEnd}")
//            if (segEnd > segStart) {
//                // 线性插值功率
//                val frac1 = if (f2 != f1) (segStart - f1) / (f2 - f1) else 0.0
//                val frac2 = if (f2 != f1) (segEnd - f1) / (f2 - f1) else 1.0
//                val pStart = p1 + (p2 - p1) * frac1
//                val pEnd = p1 + (p2 - p1) * frac2
//                val trapezoidArea = (pStart + pEnd) / 2.0 * (segEnd - segStart)
//                sum += trapezoidArea
//                Logger.d(TAG, msg = "积分落在区间: frac1=${frac1}, frac2=${frac2}, pStart=${pStart}, pEnd=${pEnd}, trapezoidArea=${trapezoidArea}, sum=${sum}")
//                trapezoidCount++
//            }
//        }
//        Logger.d(TAG, msg = "积分完成: 总梯形数=${trapezoidCount}, 总积分值=${sum.format(6)}")
//        return sum
//    }
//
//    /**
//     * 应用汉明窗
//     */
//    private fun applyHammingWindow(data: List<Double>): List<Double> {
//        return data.mapIndexed { index, value ->
//            val window = 0.54 - 0.46 * cos(2.0 * PI * index / (data.size - 1))
//            value * window
//        }
//    }
//
//    /**
//     * 线性插值
//     */
//    private fun interpolateValue(timePoints: List<Double>, values: List<Double>, targetTime: Double): Double {
//        if (targetTime <= timePoints.first()) return values.first()
//        if (targetTime >= timePoints.last()) return values.last()
//
//        for (i in 0 until timePoints.size - 1) {
//            if (targetTime in timePoints[i]..timePoints[i + 1]) {
//                val t1 = timePoints[i]
//                val t2 = timePoints[i + 1]
//                val v1 = values[i]
//                val v2 = values[i + 1]
//
//                return v1 + (v2 - v1) * (targetTime - t1) / (t2 - t1)
//            }
//        }
//
//        return values.last()
//    }
//
//    /**
//     * 完整的ECG分析流程（修改版本，使用实际时间戳进行RR间期计算）
//     */
//    fun analyzeECG(ppgDataPoints: List<PPGDataPoint>): AnalysisResult? {
//        try {
//            Logger.d(TAG, msg = "开始ECG分析...")
//            Logger.d(TAG, msg = "原始数据点数: ${ppgDataPoints.size}")
//
//            // 提取PPG值和时间戳
//            val ppgValues = extractPPGValues(ppgDataPoints)
//            val timestamps = extractTimestamps(ppgDataPoints)
//
//            // 1. 检测R波并计算RR间期（使用实际时间戳）
//            val rrIntervals = detectRPeaksAndCalculateRRWithTimestamps(ppgValues, timestamps)
//            Logger.d(TAG, msg = "检测到的RR间期数量: ${rrIntervals.size}")
//
//            if (rrIntervals.isEmpty()) {
//                throw RuntimeException("未检测到有效的R波峰值")
//            }
//
//            // 2. 计算时域参数
//            val timeDomain = calculateTimeDomainParameters(rrIntervals)
//            Logger.d(TAG, msg = "时域参数计算完成")
//
//            // 3. 计算频域参数
//            // 自适应调整重采样长度
//            val adjustedResampleLen = calculateOptimalResampleLength(rrIntervals.size)
//            val frequencyDomain = calculateFrequencyDomainParameters(rrIntervals,resampleLen = adjustedResampleLen)
//
//            return AnalysisResult(
//                timeDomain = timeDomain,
//                frequencyDomain = frequencyDomain,
//                rrIntervals = rrIntervals,
//                validIntervals = rrIntervals.size,
//                totalIntervals = ppgDataPoints.size
//            )
//        }catch (e: Exception){
//            e.printStackTrace()
//            return null
//        }
//    }
//
//    /**
//     * 检测R波峰值并计算RR间期（使用实际时间戳版本）
//     */
//    fun detectRPeaksAndCalculateRRWithTimestamps(ppgValues: List<Double>, timestamps: List<Long>): List<Double> {
//        // 应用时间窗口滤波
//        val filteredPPGValues = applyTimeBasedFilterToECGData(ppgValues)
//        Logger.d(TAG, msg = "对PPG数据应用时间窗口滤波，数据长度: ${filteredPPGValues.size}")
//
//        // 计算滤波后数据的统计信息
//        val maxVal = filteredPPGValues.maxOrNull() ?: 0.0
//        val minVal = filteredPPGValues.minOrNull() ?: 0.0
//        val meanVal = filteredPPGValues.average()
//        val stdVal = sqrt(filteredPPGValues.map { (it - meanVal).pow(2) }.average())
//
//        Logger.d(TAG, msg = "滤波后数据统计: 最大值=${maxVal}, 最小值=${minVal}, 均值=${meanVal}, 标准差=${stdVal}")
//
//        // 使用多种峰值检测策略
//        val allPeaks = detectPeaksWithMultipleStrategies(filteredPPGValues, meanVal, stdVal, maxVal)
//        Logger.d(TAG, msg = "多策略检测到的峰值数量: ${allPeaks.size}")
//
//        // 峰值验证和筛选
//        val validatedPeaks = validateAndFilterPeaks(filteredPPGValues, allPeaks)
//        Logger.d(TAG, msg = "验证后的峰值数量: ${validatedPeaks.size}")
//
//        // 计算RR间期（使用实际时间戳）
//        val rrIntervals = calculateRRIntervalsWithTimestamps(validatedPeaks, timestamps)
//        Logger.d(TAG, msg = "计算得到的RR间期数量: ${rrIntervals.size}")
//
//        // 输出RR间期统计信息
//        if (rrIntervals.isNotEmpty()) {
//            val avgRR = rrIntervals.average()
//            val minRR = rrIntervals.minOrNull() ?: 0.0
//            val maxRR = rrIntervals.maxOrNull() ?: 0.0
//            Logger.d(TAG, msg = "RR间期统计: 平均=${avgRR.format(1)}ms, 最小=${minRR.format(1)}ms, 最大=${maxRR.format(1)}ms")
//        }
//
//        return rrIntervals
//    }
//
//    /**
//     * 使用实际时间戳计算RR间期
//     */
//    private fun calculateRRIntervalsWithTimestamps(peaks: List<Int>, timestamps: List<Long>): List<Double> {
//        val rrIntervals = mutableListOf<Double>()
//
//        Logger.d(TAG, msg = "开始计算RR间期，峰值数量: ${peaks.size}")
//        Logger.d(TAG, msg = "峰值索引列表: ${peaks.take(10)}${if (peaks.size > 10) "..." else ""}")
//
//        for (i in 0 until peaks.size - 1) {
//            val peak1Index = peaks[i]
//            val peak2Index = peaks[i + 1]
//
//            // 确保索引在时间戳范围内
//            if (peak1Index < timestamps.size && peak2Index < timestamps.size) {
//                val timestamp1 = timestamps[peak1Index]
//                val timestamp2 = timestamps[peak2Index]
//
//                // 计算时间差（纳秒转换为毫秒）
//                val timeDiffNs = timestamp2 - timestamp1
//                val rrInterval = timeDiffNs / 1_000_000.0 // 转换为毫秒
//
//                //println("RR间期[${i+1}]: 峰值${i+1}(${peak1Index}, ${timestamp1}ns) -> 峰值${i+2}(${peak2Index}, ${timestamp2}ns), 间距=${rrInterval.format(1)}ms")
//
//                // 使用更宽松的RR间期范围
//                if (rrInterval in MIN_RR_INTERVAL..MAX_RR_INTERVAL) {
//                    rrIntervals.add(rrInterval)
//                    Logger.d(TAG, msg = "  -> 有效RR间期，已添加到列表")
//                } else {
//                    Logger.d(TAG, msg = "  -> 无效RR间期，超出范围(MIN_RR_INTERVAL-${MAX_RR_INTERVAL}ms)")
//                }
//            } else {
//                Logger.d(TAG, msg = "RR间期[${i+1}]: 峰值索引超出时间戳范围，跳过")
//            }
//        }
//
//        Logger.d(TAG, msg = "RR间期计算完成，有效RR间期数量: ${rrIntervals.size}")
//        if (rrIntervals.isNotEmpty()) {
//            Logger.d(TAG, msg = "有效RR间期列表: ${rrIntervals.map { it.format(1) }}")
//        }
//
//        return rrIntervals
//    }
//
//    /**
//     * 打印分析结果
//     */
//    fun printAnalysisResult(result: AnalysisResult) {
//        Logger.d(TAG, msg = "\n=== ECG分析结果 ===\n数据统计:\n  总数据点数: ${result.totalIntervals}\n" +
//                "  有效RR间期数: ${result.validIntervals}\n" +
//                "  数据利用率: ${(result.validIntervals.toDouble() / result.totalIntervals * 100).format(2)}%")
//
//        Logger.d(TAG, msg = "\n时域参数:\n  MeanNN (ms): ${result.timeDomain.meanNN.format(2)}\n" +
//                "  SDNN (ms): ${result.timeDomain.sdnn.format(2)}\n" +
//                "  RMSSD (ms): ${result.timeDomain.rmssd.format(2)}\n" +
//                "  SDSD (ms): ${result.timeDomain.sdsd.format(2)}\n" +
//                "  pNN50 (%): ${result.timeDomain.pnn50.format(2)}")
//
//        if (result.frequencyDomain.totalPower > 0) {
//            Logger.d(TAG, msg = "\n频域参数:\n  总功率 (ms²): ${result.frequencyDomain.totalPower.format(2)}\n" +
//                    "  VLF功率 (ms²): ${result.frequencyDomain.vlf.format(2)}\n" +
//                    "  LF功率 (ms²): ${result.frequencyDomain.lf.format(2)}\n" +
//                    "  HF功率 (ms²): ${result.frequencyDomain.hf.format(2)}\n" +
//                    "  LF/HF比值: ${result.frequencyDomain.lfHfRatio.format(2)}")
//        }
//    }
//
//    /**
//     * 创建并写入格式化的JSON文件
//     * @param jsonData 要写入的JSON字符串
//     * @param parentFolder 父目录
//     * @param name 文件名
//     * @param indentSpaces 缩进空格数（默认为4）
//     * @return JSON文件的绝对路径
//     */
//    fun createFormattedJsonFile(jsonData: String, parentFolder: File,name: String,indentSpaces: Int = 4): String? {
//        return try {
//            val jsonFile = File(parentFolder, "$name.json")
//
//            // 格式化JSON
//            val formattedJson = try {
//                JSONObject(jsonData).toString(indentSpaces)
//            } catch (e: Exception) {
//                // 如果不是有效JSON，直接写入原始数据
//                jsonData
//            }
//
//            // 写入文件
////            FileWriter(jsonFile).use { it.write(formattedJson) }
//            // 对于大型JSON数据
//            val writer = BufferedWriter(FileWriter(jsonFile))
//            writer.write(formattedJson)
//            writer.flush()
//            writer.close()
//
//            jsonFile.absolutePath
//        } catch (e: Exception) {
//            Logger.e(TAG, msg = "写入失败: ${e.message}")
//            null
//        }
//    }
//
//    /**
//     * 创建时间戳目录用于保存PPG数据
//     * @param context 上下文对象
//     * @param name 文件夹名称
//     */
//    fun createTimestampedFolder(context: Context,name: String): File? {
//        // 存储状态检查
//        if (!isExternalStorageWritable()) return null
//
//        return try {
//            // 确保基础目录存在
//            val baseDir = ensureBaseDirectory(context)
//
//            // 创建时间戳目录
//            createTimestampDirectory(baseDir,name)
//        } catch (e: Exception) {
//            Logger.e(TAG, msg = "创建失败: ${e.message}")
//            null
//        }
//    }
//
//    private fun isExternalStorageWritable(): Boolean {
//        return Environment.getExternalStorageState() == Environment.MEDIA_MOUNTED
//    }
//
//    private fun ensureBaseDirectory(context: Context): File {
//        val dir = context.getExternalFilesDir("HrvPpg")
//            ?: File(context.getExternalFilesDir(null), "HrvPpg").apply {
//                if (!exists()) mkdirs().also { success ->
//                    if (!success) throw IOException("无法创建HrvPpg目录")
//                }
//            }
//        Logger.d(TAG, msg = "基础目录: ${dir.absolutePath}")
//        return dir
//    }
//
//    private fun createTimestampDirectory(baseDir: File,name: String): File {
//        val timestampDir = File(baseDir, name)
//
//        return when {
//            timestampDir.exists() -> {
//                Logger.w(TAG, msg = "目录已存在: ${timestampDir.path}")
//                timestampDir
//            }
//            timestampDir.mkdirs() -> {
//                Logger.i(TAG, msg = "创建成功: ${timestampDir.path}")
//                timestampDir
//            }
//            else -> throw IOException("无法创建时间戳目录")
//        }
//    }
//
//}