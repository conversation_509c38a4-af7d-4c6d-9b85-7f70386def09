# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

# 禁止生成混合大小写的类名
-dontusemixedcaseclassnames
# 指定不去忽略非公共库的类
-dontskipnonpubliclibraryclasses
# 忽略警告信息
-ignorewarnings
# 在混淆过程中输出详细的日志信息
-verbose

# Optimization is turned off by default. <PERSON> does not like code run
# through the ProGuard optimize and preverify steps (and performs some
# of these optimizations on its own).
# 禁用代码优化阶段
-dontoptimize
# 这个过滤器是谷歌推荐的算法
#-optimizations !code/simplification/cast,!field/*,!class/merging/*
# 禁用预验证阶段,Android不需要preverify,去掉这一步能够加快混淆速度
-dontpreverify

# @ref Producing useful obfuscated stack traces http://proguard.sourceforge.net/manual/examples.html#stacktrace
-renamesourcefileattribute SourceFile
-keepattributes SourceFile,LineNumberTable

# 避免混淆泛型
-keepattributes Signature

# 保留Annotation不混淆
-keepattributes *Annotation*

# 保留异常相关的信息
-keepattributes Exceptions

# 保留四大组件，因为这些可能会提供给外部使用
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keep public class * extends android.app.backup.BackupAgentHelper
-keep public class * extends android.preference.Preference

# Webview 相关不混淆
-keepclassmembers class * extends android.webkit.WebViewClient {
        public void *(android.webkit.WebView, java.lang.String, android.graphics.Bitmap);
        public boolean *(android.webkit.WebView, java.lang.String);
}
-keepclassmembers class * extends android.webkit.WebViewClient {
        public void *(android.webkit.WebView, java.lang.String);
 }

# 保留support下的所有类及其内部类
-keep class android.support.** {*;}
-keep interface android.support.** {*;}
-keep public class * extends android.support.v4.**
-keep public class * extends android.support.v7.**
-keep public class * extends android.support.annotation.**
-dontwarn android.support.**

# 保留androidx下的所有类及其内部类
-keep class androidx.** {*;}
-keep public class * extends androidx.**
-keep interface androidx.** {*;}
-keep class com.google.android.material.** {*;}
-dontwarn androidx.**
-dontwarn com.google.android.material.**
-dontnote com.google.android.material.**

# 保留R下面的资源
-keep class **.R$* {*;}

#表示不混淆任何包含native方法的类的类名以及native方法名
-keepclasseswithmembernames class * {
    native <methods>;
}

# 保留在Activity中的方法参数是view的方法，
# 这样以来我们在layout中写的onClick就不会被影响
-keepclassmembers class * extends android.app.Activity{
    public void *(android.view.View);
}

#表示不混淆枚举中的values()和valueOf()方法
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

#表示不混淆Parcelable实现类中的CREATOR字段，包括大小写都不能变，不然整个Parcelable工作机制都会失败。
-keep class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator *;
}

# 这指定了继承Serizalizable的类的如下成员不被移除混淆
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

#表示不混淆任何一个View中的setXxx()和getXxx()方法，因为属性动画需要有相应的setter和getter的方法实现，混淆了就无法工作了。
-keep public class * extends android.view.View{
    *** get*();
    void set*(***);
    public <init>(android.content.Context);
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
}
-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
}

# Behavior是通过反射调用的，需要保留
-keep public class * extends androidx.coordinatorlayout.widget.CoordinatorLayout$Behavior {
   *;
}

##---------------Begin: proguard configuration for Gson  ----------
# Gson uses generic type information stored in a class file when working with fields. Proguard
# removes such information by default, so configure it to keep all of it.
-keepattributes Signature

# For using GSON @Expose annotation
-keepattributes *Annotation*

# Gson specific classes
-dontwarn sun.misc.**
#-keep class com.google.gson.stream.** { *; }

# Application classes that will be serialized/deserialized over Gson
#-keep class com.google.gson.examples.android.model.** { <fields>; }

# Prevent proguard from stripping interface information from TypeAdapter, TypeAdapterFactory,
# JsonSerializer, JsonDeserializer instances (so they can be used in @JsonAdapter)
-keep class * extends com.google.gson.TypeAdapter
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# Prevent R8 from leaving Data object members always null
-keepclassmembers,allowobfuscation class * {
  @com.google.gson.annotations.SerializedName <fields>;
}

# Retain generic signatures of TypeToken and its subclasses with R8 version 3.0 and higher.
-keep,allowobfuscation,allowshrinking class com.google.gson.reflect.TypeToken
-keep,allowobfuscation,allowshrinking class * extends com.google.gson.reflect.TypeToken

##---------------End: proguard configuration for Gson  ----------

# Okhttp3的混淆配置
# JSR 305 annotations are for embedding nullability information.
-dontwarn javax.annotation.**
# A resource is loaded with a relative path so the package of this class must be preserved.
-keepnames class okhttp3.internal.publicsuffix.PublicSuffixDatabase
# Animal Sniffer compileOnly dependency to ensure APIs are compatible with older versions of Java.
-dontwarn org.codehaus.mojo.animal_sniffer.*
# OkHttp platform used only on JVM and when Conscrypt dependency is available.
-dontwarn okhttp3.internal.platform.ConscryptPlatform

# OkHttp3
-dontwarn com.squareup.okhttp3.**
-keep class com.squareup.okhttp3.** { *;}
-keep class okhttp3.** { *;}
-dontwarn okio.**

# Okio
-dontwarn com.squareup.**
-dontwarn okio.**
-keep class org.codehaus.** { *; }
-keep class java.nio.** { *; }
-keep class okio.** {*;}

#retrofit2  混淆
-dontwarn javax.annotation.**
-dontwarn javax.inject.**
-dontwarn retrofit2.**
-keep class retrofit2.** { *; }

#解决报错：升级到 AGP 8.0 后 Retrofit 报错（java.lang.Class cannot be cast to java.lang.reflect.ParameterizedType）
#参考链接：https://github.com/square/retrofit/issues/3751#issuecomment-1192043644
-keep,allowobfuscation,allowshrinking interface retrofit2.Call
-keep,allowobfuscation,allowshrinking class retrofit2.Response
#With R8 full mode generic signatures are stripped for classes that are not
#kept. Suspend functions are wrapped in continuations where the type argument
#is used.
-keep,allowobfuscation,allowshrinking class kotlin.coroutines.Continuation

#Liveeventbus相关
-dontwarn com.jeremyliao.liveeventbus.**
-keep class com.jeremyliao.liveeventbus.** { *; }
-keep class androidx.lifecycle.** { *; }
-keep class androidx.arch.core.** { *; }

#glide
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep public class * extends com.bumptech.glide.module.AppGlideModule
-keep public enum com.bumptech.glide.load.resource.bitmap.ImageHeaderParser$** {
  **[] $VALUES;
  public *;
}
# 如果你的API级别<=Android API 27 则需要添加
#-dontwarn com.bumptech.glide.load.resource.bitmap.VideoDecoder

# keep native method
-keepclasseswithmembernames class * {
    native <methods>;
}

# 保护眼动追踪JNI相关类，防止混淆导致JNI回调失败
-keep class com.airdoc.mpd.gaze.track.GazeTrack {
    *;
}

# 保留 NativeCallback 类 - 这些类被JNI层直接调用，不能混淆
-keep class com.airdoc.mpd.gaze.track.GazeTrack$NativeGazeTrackCallback {
    *;
}

# 检查是否存在GazeApplied相关的JNI回调类
-keep class com.airdoc.mpd.gaze.application.GazeApplied$NativeGazeAppliedCallback {
    *;
}

# 特别保护JNI回调方法，确保方法签名不被混淆
-keepclassmembers class com.airdoc.mpd.gaze.track.GazeTrack$NativeGazeTrackCallback {
    public void onGazeServiceModeChange(int);
    public void onGazeTracking(java.util.HashMap);
    public void onPostureCalibration(java.util.HashMap);
    public void onCalibrating(java.util.HashMap);
    public void onCalibrateCoordinate(boolean, boolean, float, int, int, float, float, int);
}

# 特别保护GazeApplied的JNI回调方法
-keepclassmembers class com.airdoc.mpd.gaze.application.GazeApplied$NativeGazeAppliedCallback {
    public void onGazeAppliedModeChange(int);
}

# 保护眼动追踪相关的数据类，防止Gson序列化/反序列化失败
-keep class com.airdoc.mpd.gaze.bean.** {
    *;
}
-keep class com.airdoc.mpd.gaze.enumeration.** {
    *;
}
-keep class com.airdoc.mpd.device.bean.** {
    *;
}
-keep class com.airdoc.mpd.user.bean.** {
    *;
}
-keep class com.airdoc.mpd.update.bean.** {
    *;
}
-keep class com.airdoc.mpd.media.bean.** {
    *;
}

# 保护眼动追踪管理类
-keep class com.airdoc.mpd.gaze.GazeTrackingManager {
    *;
}
-keep class com.airdoc.mpd.gaze.track.TrackingManager {
    *;
}

# 保护其他JNI相关类
-keep class com.airdoc.mpd.gaze.application.GazeApplied {
    *;
}
-keep class com.airdoc.mpd.gaze.upload.UploadCloud {
    *;
}

#MPAndroidChart
-dontwarn com.github.mikephil.**
-keep class com.github.mikephil.**{ *; }

##---------------Begin: proguard configuration for Alibaba Cloud MQTT ----------
# linkkit API
-keep class com.aliyun.alink.**{*;}
-keep class com.aliyun.linksdk.**{*;}
-dontwarn com.aliyun.**
-dontwarn com.alibaba.**
-dontwarn com.alipay.**
-dontwarn com.ut.**

# keep native method
-keepclasseswithmembernames class * {
    native <methods>;
}

# keep netty
-keepattributes Signature,InnerClasses
-keepclasseswithmembers class io.netty.** {
    *;
}
-keepnames class io.netty.** {
    *;
}
-dontwarn io.netty.**
-dontwarn sun.**

# keep mqtt
-keep public class org.eclipse.paho.**{*;}

# keep fastjson
-dontwarn com.alibaba.fastjson.**
-keep class com.alibaba.fastjson.**{*;}

# keep gson
-keep class com.google.gson.** { *;}

# keep network core
-keep class com.http.**{*;}
-keep class org.mozilla.**{*;}

# keep okhttp
-dontwarn okhttp3.**
-dontwarn okio.**
-dontwarn javax.annotation.**
-dontwarn org.mozilla.**

-keep class okio.**{*;}
-keep class okhttp3.**{*;}
-keep class org.apache.commons.codec.**{*;}

-keep class com.aliyun.alink.devicesdk.demo.FileProvider{*;}
-keep class android.support.**{*;}
-keep class android.os.**{*;}

##---------------End: proguard configuration for Alibaba Cloud MQTT  ----------

##---------------Begin: LibCommon ----------
-keep class com.airdoc.component.common.net.entity.** {
    *;
}
-keep class com.airdoc.component.common.stat.core.event.bean.** {
    *;
}
##---------------End: LibCommon ----------

-keep class com.airdoc.mpd.update.bean.** {
    *;
}

-keep class com.airdoc.mpd.user.bean.** {
    *;
}

-keep class com.airdoc.mpd.device.bean.** {
    *;
}

-keep class com.airdoc.mpd.media.bean.** {
    *;
}


##---------------Begin: LePu血氧 ----------
-keep class com.lepu.blepro.ext.**{*;}
-keep class com.lepu.blepro.constants.**{*;}
-keep class com.lepu.blepro.event.**{*;}
-keep class com.lepu.blepro.objs.**{*;}
-keep class com.lepu.blepro.utils.DateUtil{*;}
-keep class com.lepu.blepro.utils.HexString{*;}
-keep class com.lepu.blepro.utils.StringUtilsKt{*;}
-keep class com.lepu.blepro.utils.DecompressUtil{*;}
-keep class com.lepu.blepro.utils.FilterUtil{*;}
-keep class com.lepu.blepro.observer.**{*;}
##---------------End: LePu血氧 ----------

##---------------Begin: Stream Log (required by LePu) ----------
-keep class io.getstream.log.**{*;}
-dontwarn io.getstream.log.**
##---------------End: Stream Log ----------
